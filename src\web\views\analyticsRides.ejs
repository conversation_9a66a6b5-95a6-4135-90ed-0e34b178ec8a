<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include('partials/head', { title: 'Analytics - Rides' }) %>
    <script src="../assets/js/plugin/chart.js/chart.min.js"></script>
  </head>
  <body>
    <div class="wrapper">
      <%- include('partials/sidebar', { activePage: 'analyticsRides', activeGroup: 'analytics' }) %>

      <div class="main-panel">
        <div class="main-header">
          <div class="main-header-logo">
            <%- include('partials/logoHeader') %>
          </div>
          <%- include('partials/navbar') %>
        </div>
        
        <div class="rides-container mt-6">
          <h2>Rides Statistics</h2>
          <div class="row" id="charts-row">
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">Rides per hour</div>
                <div class="card-body">
                  <%- include('analytics/rides/rides_per_hour.ejs') %>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">Rides per week (by day)</div>
                <div class="card-body">
                  <%- include('analytics/rides/rides_per_week.ejs') %>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">Rides by month</div>
                <div class="card-body">
                  <%- include('analytics/rides/rides_per_month.ejs') %>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card">
                <div class="card-header">Rides per Km</div>
                <div class="card-body">
                  <%- include('analytics/rides/rides_per_km.ejs') %>
                </div>
              </div>
            </div>
          </div>
        </div>
        <%- include('partials/footer') %>
      </div>
      <%- include('partials/settings') %>
    </div>

    <script>
      // Hacer los datos disponibles globalmente
      const ridesData = JSON.parse('<%- JSON.stringify(ridesData || {}) %>');
    </script>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        createRidesPerHourChart();    
        createRidesPerWeekChart();    
        createRidesPerMonthChart();   
        createRidesPerKmChart();      
      });
    </script>

  </body>
</html>